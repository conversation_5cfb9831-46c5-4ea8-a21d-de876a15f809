/* Announcements Management Styles */

/* Main Container */
.announcements-table-container {
    background: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    margin-bottom: 20px;
}

/* Table Header */
.table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 25px;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-bottom: 1px solid #dee2e6;
}

.table-header h2 {
    margin: 0;
    color: #052F11;
    font-size: 20px;
    font-weight: 600;
}

.table-actions {
    display: flex;
    gap: 10px;
    align-items: center;
}

/* Buttons */
.btn {
    padding: 8px 16px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 6px;
    text-decoration: none;
}

.btn-primary {
    background: linear-gradient(135deg, #052F11, #1e5631);
    color: white;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #1e5631, #2e7d32);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(5, 47, 17, 0.3);
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #5a6268;
}

.btn-danger {
    background: #dc3545;
    color: white;
}

.btn-danger:hover {
    background: #c82333;
}

/* Table Styles */
.table-responsive {
    overflow-x: auto;
}

.announcements-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 14px;
}

.announcements-table th {
    background: #f8f9fa;
    color: #495057;
    font-weight: 600;
    padding: 15px 20px;
    text-align: left;
    border-bottom: 2px solid #dee2e6;
    white-space: nowrap;
}

.announcements-table td {
    padding: 15px 20px;
    border-bottom: 1px solid #dee2e6;
    vertical-align: top;
}

.announcements-table tr:hover {
    background: #f8f9fa;
}

/* Table Content */
.announcement-title {
    font-weight: 600;
    color: #052F11;
    margin-bottom: 4px;
}

.content-preview {
    color: #6c757d;
    line-height: 1.4;
}

.date-info {
    text-align: center;
}

.date-info .date {
    font-weight: 500;
    color: #495057;
    margin-bottom: 2px;
}

.date-info .time {
    font-size: 12px;
    color: #6c757d;
}

/* Action Buttons */
.action-buttons {
    display: flex;
    gap: 8px;
    justify-content: center;
}

.btn-action {
    width: 32px;
    height: 32px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    font-size: 14px;
}

.btn-edit {
    background: #17a2b8;
    color: white;
}

.btn-edit:hover {
    background: #138496;
    transform: translateY(-1px);
}

.btn-delete {
    background: #dc3545;
    color: white;
}

.btn-delete:hover {
    background: #c82333;
    transform: translateY(-1px);
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    animation: fadeIn 0.3s ease;
}

.modal-content {
    background-color: white;
    margin: 5% auto;
    border-radius: 10px;
    width: 90%;
    max-width: 600px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    animation: slideIn 0.3s ease;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 25px;
    border-bottom: 1px solid #dee2e6;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-radius: 10px 10px 0 0;
}

.modal-header h2 {
    margin: 0;
    color: #052F11;
    font-size: 18px;
    font-weight: 600;
}

.close {
    color: #6c757d;
    font-size: 24px;
    font-weight: bold;
    cursor: pointer;
    transition: color 0.3s ease;
}

.close:hover {
    color: #052F11;
}

.modal-body {
    padding: 25px;
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    padding: 20px 25px;
    border-top: 1px solid #dee2e6;
    background: #f8f9fa;
    border-radius: 0 0 10px 10px;
}

/* Form Styles */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 6px;
    font-weight: 500;
    color: #495057;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid #ced4da;
    border-radius: 6px;
    font-size: 14px;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #052F11;
    box-shadow: 0 0 0 2px rgba(5, 47, 17, 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 120px;
}

/* Confirmation Modal */
.confirmation-modal {
    max-width: 400px;
}

.confirmation-content {
    text-align: center;
    padding: 20px 0;
}

.warning-icon {
    font-size: 48px;
    color: #ffc107;
    margin-bottom: 15px;
}

.confirmation-content p {
    margin: 0;
    color: #495057;
    line-height: 1.5;
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .table-header {
        flex-direction: column;
        gap: 15px;
        align-items: stretch;
    }
    
    .modal-content {
        margin: 10% auto;
        width: 95%;
    }
    
    .modal-header,
    .modal-body,
    .modal-footer {
        padding: 15px 20px;
    }
    
    .announcements-table th,
    .announcements-table td {
        padding: 10px 15px;
    }
    
    .action-buttons {
        flex-direction: column;
    }
}

/* Empty State */
.no-data {
    text-align: center;
    padding: 40px 20px;
    color: #6c757d;
}

.no-data i {
    font-size: 48px;
    margin-bottom: 15px;
    opacity: 0.5;
}

.no-data p {
    margin: 0;
    font-size: 16px;
}
