/* Announcements Page - Logo Color Scheme */

/* Status Badges */
.status-badge {
    display: inline-flex;
    align-items: center;
    padding: 4px 10px;
    border-radius: 4px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-badge.published {
    background-color: rgba(5, 47, 17, 0.1);
    color: #052F11;
    border: 1px solid rgba(5, 47, 17, 0.3);
}

.status-badge.draft {
    background-color: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

.status-badge.archived {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

/* Action Buttons */
.action-btn {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    padding: 6px 10px;
    margin: 0 2px;
    border: 1px solid #052F11;
    background-color: #052F11;
    color: white;
    font-size: 12px;
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    border-radius: 3px;
    transition: all 0.2s ease;
}

.action-btn:hover {
    background-color: rgba(5, 47, 17, 0.8);
    color: white;
    text-decoration: none;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(5, 47, 17, 0.3);
}

.action-btn.view {
    background-color: #052F11;
    border-color: #052F11;
}

.action-btn.edit {
    background-color: rgba(5, 47, 17, 0.7);
    border-color: rgba(5, 47, 17, 0.7);
}

.action-btn.delete {
    background-color: #dc3545;
    border-color: #dc3545;
}

.action-btn.delete:hover {
    background-color: #c82333;
    border-color: #c82333;
}

.action-btn.publish {
    background-color: rgba(5, 47, 17, 0.8);
    border-color: rgba(5, 47, 17, 0.8);
}

/* Add Announcement Button */
.add-announcement-btn {
    background: #052F11;
    color: white;
    border: 1px solid #052F11;
    padding: 12px 20px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 8px;
    text-decoration: none;
    transition: all 0.3s ease;
    border-radius: 4px;
}

.add-announcement-btn:hover {
    background: rgba(5, 47, 17, 0.8);
    color: white;
    text-decoration: none;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(5, 47, 17, 0.3);
}

/* Primary Buttons */
.btn-primary {
    background: #052F11;
    color: white;
    border: 1px solid #052F11;
    padding: 10px 16px;
    font-size: 14px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
    border-radius: 4px;
}

.btn-primary:hover {
    background: rgba(5, 47, 17, 0.8);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(5, 47, 17, 0.3);
}

/* Secondary Buttons */
.btn-secondary {
    background: rgba(5, 47, 17, 0.6);
    color: white;
    border: 1px solid rgba(5, 47, 17, 0.6);
    padding: 10px 16px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
    border-radius: 4px;
}

.btn-secondary:hover {
    background: #052F11;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(5, 47, 17, 0.3);
}

/* Priority Badges */
.priority-badge {
    padding: 4px 8px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    border-radius: 4px;
    letter-spacing: 0.5px;
}

.priority-badge.high {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.priority-badge.medium {
    background-color: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

.priority-badge.low {
    background-color: rgba(5, 47, 17, 0.1);
    color: #052F11;
    border: 1px solid rgba(5, 47, 17, 0.3);
}

/* Table Styling */
.announcements-table {
    width: 100%;
    border-collapse: collapse;
}

.announcements-table th {
    background-color: #f8f9fa;
    padding: 15px;
    text-align: left;
    font-weight: 600;
    color: #333;
    border-bottom: 1px solid #dee2e6;
    font-size: 14px;
}

.announcements-table td {
    padding: 15px;
    border-bottom: 1px solid #eee;
    font-size: 14px;
    color: #333;
    vertical-align: middle;
}

.announcements-table tr:last-child td {
    border-bottom: none;
}

/* Hover effects for table rows */
.announcements-table tbody tr:hover {
    background-color: rgba(5, 47, 17, 0.05);
}

/* Announcement Type Indicators */
.announcement-type {
    padding: 4px 8px;
    font-size: 12px;
    background: rgba(5, 47, 17, 0.1);
    color: #052F11;
    border: 1px solid rgba(5, 47, 17, 0.3);
    border-radius: 4px;
    font-weight: 500;
}

/* Date Styling */
.announcement-date {
    color: #666;
    font-size: 13px;
}

/* Title Styling */
.announcement-title {
    font-weight: 600;
    color: #052F11;
    margin-bottom: 4px;
}

/* Content Preview */
.announcement-content {
    color: #666;
    font-size: 13px;
    max-width: 300px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* Filter Buttons */
.filter-btn {
    padding: 8px 16px;
    border: 1px solid #052F11;
    background-color: white;
    color: #052F11;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
    margin: 0 5px;
    border-radius: 4px;
}

.filter-btn:hover {
    background-color: #052F11;
    color: white;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(5, 47, 17, 0.3);
}

.filter-btn.active {
    background-color: #052F11;
    color: white;
}
