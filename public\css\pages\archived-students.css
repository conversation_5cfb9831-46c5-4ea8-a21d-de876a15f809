/* Archived Students Page Styles */

/* Archive Statistics */
.archive-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: linear-gradient(135deg, #ffffff, #f8f9fa);
    border: 1px solid #e9ecef;
    border-radius: 12px;
    padding: 20px;
    display: flex;
    align-items: center;
    gap: 15px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border-color: #1e5631;
}

.stat-icon {
    width: 50px;
    height: 50px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    color: white;
    background: linear-gradient(135deg, #1e5631, #2e7d32);
}

.stat-content h3 {
    margin: 0 0 5px 0;
    font-size: 24px;
    font-weight: 700;
    color: #1e5631;
}

.stat-content p {
    margin: 0;
    font-size: 14px;
    color: #666;
    font-weight: 500;
}

/* Archive Filters */
.archive-filters {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 25px;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    align-items: end;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.filter-group label {
    font-weight: 600;
    color: #333;
    font-size: 14px;
}

.filter-group select,
.filter-group input {
    padding: 10px 12px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 14px;
    transition: all 0.3s ease;
    background-color: #fafafa;
}

.filter-group select:focus,
.filter-group input:focus {
    outline: none;
    border-color: #1e5631;
    background-color: white;
    box-shadow: 0 0 0 3px rgba(30, 86, 49, 0.1);
}

/* Table Styles */
.table-container {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.table-header {
    background: linear-gradient(135deg, #f8f9fa, #ffffff);
    padding: 20px 25px;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.table-header h3 {
    margin: 0;
    color: #333;
    font-size: 18px;
    font-weight: 600;
}

.table-actions {
    display: flex;
    gap: 10px;
}

.export-btn {
    background: linear-gradient(135deg, #17a2b8, #138496);
    color: white;
    border: none;
    padding: 10px 16px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
}

.export-btn:hover {
    background: linear-gradient(135deg, #138496, #117a8b);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(23, 162, 184, 0.3);
}

.table-wrapper {
    overflow-x: auto;
    max-width: 100%;
}

.archived-students-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 14px;
    table-layout: fixed;
}

/* Optimized column widths for 4 columns */
.archived-students-table th:nth-child(1), /* Student ID */
.archived-students-table td:nth-child(1) {
    width: 15%;
}

.archived-students-table th:nth-child(2), /* Name */
.archived-students-table td:nth-child(2) {
    width: 45%;
}

.archived-students-table th:nth-child(3), /* Scholarship Type */
.archived-students-table td:nth-child(3) {
    width: 25%;
}

.archived-students-table th:nth-child(4), /* Actions */
.archived-students-table td:nth-child(4) {
    width: 15%;
    text-align: center;
}



.archived-students-table th {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    color: #495057;
    font-weight: 600;
    padding: 15px 12px;
    text-align: left;
    border-bottom: 2px solid #dee2e6;
    white-space: nowrap;
}

.archived-students-table td {
    padding: 15px 12px;
    border-bottom: 1px solid #f1f3f4;
    vertical-align: middle;
}

.archived-students-table tbody tr {
    transition: all 0.2s ease;
}

.archived-students-table tbody tr:hover {
    background-color: #f8f9fa;
}

/* Student Info Styles */
.student-info {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.student-name {
    font-weight: 600;
    color: #333;
}

.student-email {
    color: #666;
    font-size: 12px;
}

.course-info {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.course-name {
    font-weight: 500;
    color: #333;
}

.department {
    color: #666;
    font-size: 12px;
}

/* Scholarship Type Badges */
.scholarship-type {
    display: inline-block;
    padding: 4px 10px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    border: 1px solid;
}

.scholarship-type.ched {
    background: linear-gradient(135deg, #e3f2fd, #e1f5fe);
    color: #0277bd;
    border-color: #29b6f6;
}

.scholarship-type.presidents {
    background: linear-gradient(135deg, #f3e5f5, #fce4ec);
    color: #7b1fa2;
    border-color: #ba68c8;
}

.scholarship-type.employees {
    background: linear-gradient(135deg, #e8f5e8, #f1f8e9);
    color: #2e7d32;
    border-color: #66bb6a;
}

.scholarship-type.private {
    background: linear-gradient(135deg, #fff8e1, #fff3e0);
    color: #ef6c00;
    border-color: #ffcc02;
}

/* GWA Badge */
.gwa-badge {
    display: inline-block;
    padding: 4px 8px;
    background: linear-gradient(135deg, #e8f5e8, #f1f8e9);
    color: #2e7d32;
    border-radius: 8px;
    font-weight: 600;
    font-size: 12px;
}

/* Period Info */
.period-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.semester-badge {
    display: inline-block;
    padding: 3px 8px;
    background: linear-gradient(135deg, #e8f5e8, #f1f8e9);
    color: #2e7d32;
    border-radius: 10px;
    font-size: 11px;
    font-weight: 600;
    text-align: center;
}

.academic-year {
    display: inline-block;
    padding: 3px 8px;
    background: linear-gradient(135deg, #e3f2fd, #e1f5fe);
    color: #0277bd;
    border-radius: 10px;
    font-size: 11px;
    font-weight: 600;
    text-align: center;
}

/* Archived Date */
.archived-date {
    font-weight: 500;
    color: #333;
    display: block;
}

.archived-time {
    color: #666;
    font-size: 11px;
}

/* Action Buttons */
.action-btn {
    background: none;
    border: 1px solid #dee2e6;
    padding: 6px 8px;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
    margin: 0 2px;
}

.action-btn.view {
    color: #17a2b8;
    border-color: #17a2b8;
}

.action-btn.view:hover {
    background: #17a2b8;
    color: white;
    transform: translateY(-1px);
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #6c757d;
}

.empty-state i {
    font-size: 48px;
    margin-bottom: 20px;
    opacity: 0.5;
}

.empty-state h3 {
    margin: 0 0 10px 0;
    font-size: 18px;
    font-weight: 600;
}

.empty-state p {
    margin: 0;
    font-size: 14px;
}

.text-muted {
    color: #6c757d;
    font-style: italic;
}

/* Add Student Button */
.btn-primary {
    background: linear-gradient(135deg, #1e5631, #2e7d32);
    color: white;
    border: none;
    padding: 10px 16px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #2e7d32, #388e3c);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(30, 86, 49, 0.3);
}

/* Modal Styles */
.modal {
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(4px);
    animation: fadeIn 0.3s ease;
}

.modal-content {
    background-color: white;
    margin: 2% auto;
    padding: 0;
    border-radius: 12px;
    width: 90%;
    max-width: 800px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    animation: slideIn 0.3s ease;
}

.modal-header {
    background: linear-gradient(135deg, #f8f9fa, #ffffff);
    padding: 20px 25px;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-radius: 12px 12px 0 0;
}

.modal-header h2 {
    margin: 0;
    color: #333;
    font-size: 20px;
    font-weight: 600;
}

.close {
    color: #aaa;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    transition: color 0.3s ease;
}

.close:hover,
.close:focus {
    color: #333;
}

.modal-body {
    padding: 25px;
}

.modal-footer {
    background: #f8f9fa;
    padding: 20px 25px;
    border-top: 1px solid #e9ecef;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    border-radius: 0 0 12px 12px;
}

/* Form Styles */
.form-section {
    margin-bottom: 25px;
}

.form-section h3 {
    margin: 0 0 15px 0;
    color: #333;
    font-size: 16px;
    font-weight: 600;
    padding-bottom: 8px;
    border-bottom: 2px solid #e9ecef;
}

.form-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 15px;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.form-group label {
    font-weight: 600;
    color: #333;
    font-size: 14px;
}

.form-group input,
.form-group select,
.form-group textarea {
    padding: 10px 12px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 14px;
    transition: all 0.3s ease;
    background-color: #fafafa;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #1e5631;
    background-color: white;
    box-shadow: 0 0 0 3px rgba(30, 86, 49, 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 80px;
}

.btn-secondary {
    background: #6c757d;
    color: white;
    border: none;
    padding: 10px 16px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-secondary:hover {
    background: #5a6268;
    transform: translateY(-1px);
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .archive-stats {
        grid-template-columns: 1fr;
    }

    .archive-filters {
        grid-template-columns: 1fr;
    }

    .table-header {
        flex-direction: column;
        gap: 15px;
        align-items: flex-start;
    }

    .archived-students-table {
        font-size: 12px;
    }

    .archived-students-table th,
    .archived-students-table td {
        padding: 10px 8px;
    }

    .stat-card {
        padding: 15px;
    }

    .stat-icon {
        width: 40px;
        height: 40px;
        font-size: 16px;
    }

    .stat-content h3 {
        font-size: 20px;
    }
}
