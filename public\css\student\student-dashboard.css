/* ===================================
   STUDENT DASHBOARD STYLES
   =================================== */

/* CSS Variables */
:root {
    --primary-color: #052F11;
    --primary-dark: #052F11;
    --primary-light: rgba(5, 47, 17, 0.1);
    --accent-color: #ffc107;
    --text-color: #333;
    --light-gray: #f5f5f5;
    --border-color: #ddd;
    --card-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

/* ===================================
   BASE STYLES
   =================================== */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
    color: var(--text-color);
    margin: 0;
    padding: 0;
    min-height: 100vh;
}

/* ===================================
   HEADER STYLES
   =================================== */
.header {
    background-color: var(--primary-color);
    color: white;
    padding: 15px 0;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.header-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px;
}

.university-logo {
    display: flex;
    align-items: center;
}

.logo-img {
    width: 50px;
    height: 50px;
    margin-right: 15px;
}

.university-name {
    margin: 0;
    font-size: 18px;
    font-weight: 700;
    line-height: 1.2;
}

.office-name {
    font-size: 14px;
    margin: 0;
    opacity: 0.9;
}

.logout-btn {
    background-color: rgba(255, 255, 255, 0.15);
    color: white;
    border: none;
    padding: 8px 15px;
    border-radius: 5px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
}

.logout-btn:hover {
    background-color: rgba(255, 255, 255, 0.25);
    color: white;
}

.logout-btn i {
    margin-right: 5px;
}

/* ===================================
   DASHBOARD HEADER
   =================================== */
.dashboard-header {
    background-color: #ffffff;
    padding: 15px 0;
    border-bottom: 1px solid var(--border-color);
    margin-bottom: 25px;
}

.dashboard-title {
    font-size: 22px;
    font-weight: 600;
    margin: 0;
    color: var(--primary-color);
    text-align: center;
}

/* ===================================
   CONTAINER STYLES
   =================================== */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 30px 20px;
}

/* Main content area */
.main-content {
    padding: 20px 0;
}

/* ===================================
   WELCOME SECTION
   =================================== */
.welcome-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 30px 0;
    padding: 25px;
    background-color: white;
    border-radius: 10px;
    box-shadow: var(--card-shadow);
}

@media (max-width: 768px) {
    .welcome-section {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }

    .welcome-greeting {
        font-size: 18px;
    }

    .student-id-display {
        font-size: 13px;
    }

    /* Settings Modal Mobile Styles */
    .settings-modal-content {
        width: 95%;
        margin: 5% auto;
        max-height: 85vh;
    }

    .settings-modal-header {
        padding: 15px 20px;
    }

    .settings-modal-header h2 {
        font-size: 18px;
    }

    .settings-tabs {
        flex-direction: column;
    }

    .settings-tab {
        padding: 12px 15px;
        text-align: left;
    }

    .settings-modal-body {
        padding: 20px;
    }

    .detail-row {
        grid-template-columns: 1fr;
        gap: 5px;
        padding: 10px 0;
    }

    .detail-row label {
        font-size: 13px;
        color: #666;
    }

    .form-actions {
        flex-direction: column;
    }

    .btn-primary,
    .btn-secondary {
        width: 100%;
        justify-content: center;
    }

    /* Toast notifications mobile styles */
    .toast-container {
        top: 10px;
        right: 10px;
        left: 10px;
        max-width: none;
    }

    .toast {
        padding: 15px;
        gap: 12px;
    }

    .toast-icon {
        width: 35px;
        height: 35px;
        font-size: 16px;
    }

    .toast-title {
        font-size: 15px;
    }

    .toast-message {
        font-size: 13px;
    }
}

.welcome-text {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.welcome-greeting {
    font-size: 20px;
    font-weight: 500;
    color: var(--text-color);
}

.student-id-display {
    font-size: 14px;
    font-weight: 400;
    color: #666;
    font-family: 'Courier New', monospace;
    background: #f8f9fa;
    padding: 4px 8px;
    border-radius: 4px;
    border: 1px solid #e9ecef;
    display: inline-block;
    width: fit-content;
}

.user-actions {
    display: flex;
    gap: 15px;
}

.action-link {
    display: inline-flex;
    align-items: center;
    color: var(--primary-color);
    text-decoration: none;
    font-size: 15px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.action-link:hover {
    color: var(--primary-dark);
    text-decoration: underline;
}

.action-link i {
    margin-right: 5px;
}

/* ===================================
   CARD STYLES
   =================================== */
.dashboard-card {
    background-color: white;
    border-radius: 10px;
    box-shadow: var(--card-shadow);
    margin-bottom: 30px;
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.dashboard-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.12);
}

.card-header {
    background-color: var(--primary-color);
    color: white;
    padding: 15px 20px;
    font-size: 18px;
    font-weight: 600;
    display: flex;
    align-items: center;
}

.card-header i {
    margin-right: 10px;
    font-size: 20px;
}



.card-body {
    padding: 20px;
}

/* ===================================
   SCHOLARSHIP CARDS
   =================================== */
.scholarship-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 25px;
    margin-top: 10px;
}

.scholarship-card {
    background-color: white;
    border-radius: 12px;
    border: 2px solid var(--border-color);
    padding: 25px;
    transition: all 0.3s ease;
    cursor: pointer;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.scholarship-card:hover {
    border-color: var(--primary-color);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
}

.scholarship-card.active {
    border-color: var(--primary-color);
    background-color: var(--primary-light);
}

.scholarship-title {
    font-size: 18px;
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: 10px;
}

.scholarship-description {
    font-size: 14px;
    color: #555;
    margin-bottom: 15px;
}

.apply-btn {
    background-color: #FFD700;
    color: #333;
    border: none;
    padding: 10px 18px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    box-shadow: 0 2px 8px rgba(255, 215, 0, 0.3);
}

.apply-btn:hover {
    background-color: #E6C200;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(255, 215, 0, 0.4);
}

.apply-btn i {
    margin-right: 5px;
}

/* ===================================
   ALERT STYLES
   =================================== */
.alert {
    padding: 15px;
    margin-bottom: 20px;
    border: 1px solid transparent;
    border-radius: 5px;
}

.alert-danger {
    color: #721c24;
    background-color: #f8d7da;
    border-color: #f5c6cb;
}

.alert-success {
    color: #155724;
    background-color: #d4edda;
    border-color: #c3e6cb;
}

/* ===================================
   APPLICATION FORM STYLES
   =================================== */
.application-form-container {
    display: none;
    margin-top: 30px;
    background-color: white;
    border-radius: 10px;
    box-shadow: var(--card-shadow);
    overflow: hidden;
}

.application-form-container.active {
    display: block;
}

.form-header {
    background-color: var(--primary-color);
    color: white;
    padding: 20px;
    font-size: 20px;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.form-header i {
    margin-right: 10px;
}

.close-form-btn {
    background: none;
    border: none;
    color: white;
    font-size: 20px;
    cursor: pointer;
    padding: 5px;
    border-radius: 3px;
    transition: background-color 0.3s ease;
}

.close-form-btn:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

.form-body {
    padding: 30px;
}

.form-section-title {
    font-size: 18px;
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid var(--primary-light);
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: var(--text-color);
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 12px;
    border: 1px solid var(--border-color);
    border-radius: 5px;
    font-size: 14px;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(30, 86, 49, 0.1);
}

.form-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.submit-btn {
    background-color: var(--primary-color);
    color: white;
    border: none;
    padding: 12px 30px;
    border-radius: 5px;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
}

.submit-btn:hover {
    background-color: var(--primary-dark);
    transform: translateY(-2px);
}

.submit-btn i {
    margin-right: 8px;
}

/* Application Forms Header */
.application-forms-header {
    flex: 1;
}

.application-forms-header h3 {
    margin: 0 0 15px 0;
    font-size: 24px;
    font-weight: 700;
    color: #2c5530;
}

/* Form Title */
.form-title {
    margin-bottom: 15px;
}

.form-title h4 {
    color: #2c5530;
    font-size: 18px;
    font-weight: 600;
    margin: 0;
    padding-bottom: 8px;
    border-bottom: 2px solid #2c5530;
    display: inline-block;
}

/* Form Description */
.form-description {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 20px;
    border-left: 4px solid #2c5530;
}

.form-description p {
    margin: 0;
    color: #495057;
    font-size: 14px;
    line-height: 1.5;
}

/* Full Width Form Group */
.form-group.full-width {
    flex: 1 1 100%;
}

/* Student ID Group */
.form-group.student-id-group {
    flex: 0 0 300px;
    max-width: 300px;
}

/* ===================================
   RADIO BUTTON STYLES
   =================================== */
.radio-group-inline {
    display: flex;
    gap: 20px;
    margin-top: 5px;
}

.radio-option-inline {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
}

.radio-option-inline input[type="radio"] {
    margin: 0;
    width: 18px;
    height: 18px;
}

.radio-option-inline .radio-label {
    font-size: 14px;
    color: #333;
}

.radio-group {
    display: flex;
    gap: 20px;
    margin-top: 8px;
}

.radio-option {
    display: flex;
    align-items: center;
    cursor: pointer;
    padding: 12px 16px;
    border: 2px solid var(--border-color);
    border-radius: 8px;
    transition: all 0.3s ease;
    background-color: white;
}

.radio-option:hover {
    border-color: var(--primary-color);
    background-color: var(--primary-light);
}

.radio-option input[type="radio"] {
    margin-right: 10px;
    width: 18px;
    height: 18px;
    accent-color: var(--primary-color);
}

.radio-option.selected {
    border-color: var(--primary-color);
    background-color: var(--primary-light);
}

.radio-label {
    font-weight: 500;
    color: var(--text-color);
}

/* ===================================
   VALIDATION STYLES
   =================================== */
.form-group input.error {
    border-color: #dc3545;
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

.student-id-error {
    color: #dc3545;
    font-size: 12px;
    margin-top: 5px;
    padding: 8px 12px;
    background-color: #f8d7da;
    border: 1px solid #f5c6cb;
    border-radius: 4px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.student-id-error:before {
    content: "⚠️";
    font-size: 14px;
}

.form-group input.valid {
    border-color: #28a745;
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
}

.student-id-success {
    color: #155724;
    font-size: 12px;
    margin-top: 5px;
    padding: 8px 12px;
    background-color: #d4edda;
    border: 1px solid #c3e6cb;
    border-radius: 4px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.student-id-success:before {
    content: "✅";
    font-size: 14px;
}

input[name="student_id"].error {
    border-color: #dc3545 !important;
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25) !important;
    background-color: #fff5f5;
}

input[name="student_id"].valid {
    border-color: #28a745 !important;
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25) !important;
    background-color: #f8fff8;
}

/* ===================================
   FILE UPLOAD STYLES
   =================================== */
.file-upload-container {
    position: relative;
    border: 2px dashed #dee2e6;
    border-radius: 8px;
    padding: 20px;
    text-align: center;
    background: #f8f9fa;
    transition: all 0.3s ease;
    cursor: pointer;
}

.file-upload-container:hover {
    border-color: #2c5530;
    background: #f0f8f0;
}

.file-upload-container input[type="file"] {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    cursor: pointer;
}

.file-upload-info {
    pointer-events: none;
}

.file-upload-info i {
    font-size: 24px;
    color: #6c757d;
    margin-bottom: 10px;
}

.file-upload-info p {
    margin: 0 0 5px 0;
    color: #495057;
    font-weight: 500;
}

.file-upload-info small {
    color: #6c757d;
    font-size: 12px;
}

.file-upload-container.dragover {
    border-color: #2c5530;
    background: #e8f5e8;
}

.file-upload-area {
    cursor: pointer;
}

.file-upload-area i {
    font-size: 48px;
    color: #6c757d;
    margin-bottom: 15px;
}

.file-upload-area p {
    font-size: 16px;
    color: #495057;
    margin-bottom: 8px;
}

.file-upload-area small {
    color: #6c757d;
    font-size: 12px;
}

/* Uploaded Files List */
.uploaded-files-list {
    margin-top: 15px;
    text-align: left;
}

.uploaded-file-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 15px;
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    margin-bottom: 8px;
}

.uploaded-file-info {
    display: flex;
    align-items: center;
    gap: 10px;
}

.uploaded-file-info i {
    color: #2c5530;
    font-size: 18px;
}

.uploaded-file-name {
    font-size: 14px;
    color: #495057;
    font-weight: 500;
}

.uploaded-file-size {
    font-size: 12px;
    color: #6c757d;
}

.remove-file-btn {
    background: none;
    border: none;
    color: #dc3545;
    cursor: pointer;
    padding: 4px 8px;
    border-radius: 4px;
    transition: background-color 0.2s ease;
}

.remove-file-btn:hover {
    background-color: #f8d7da;
}

/* Document Checklist Styles */
.document-checklist {
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 20px;
    margin-top: 10px;
}

.checklist-item {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
    padding: 8px 0;
}

.checklist-item:last-child {
    margin-bottom: 0;
}

.checklist-item input[type="checkbox"] {
    margin-right: 12px;
    width: 18px;
    height: 18px;
    accent-color: var(--primary-color);
}

.checklist-item label {
    font-size: 14px;
    color: #495057;
    cursor: pointer;
    margin-bottom: 0;
}

.checklist-item input[type="checkbox"]:checked+label {
    color: var(--primary-color);
    font-weight: 500;
}

/* ===================================
   CONDITIONAL FIELDS & ANIMATIONS
   =================================== */
.bsu-fields,
.college-fields,
.ched-bsu-fields,
.ched-college-fields,
.ched-strand-field,
.subjects-section {
    transition: all 0.3s ease;
    overflow: hidden;
}

.ched-bsu-fields.show,
.ched-college-fields.show,
.subjects-section.show {
    display: block !important;
    animation: slideDown 0.3s ease;
}

@keyframes slideDown {
    from {
        opacity: 0;
        max-height: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        max-height: 500px;
        transform: translateY(0);
    }
}

/* ===================================
   SUBJECTS AND GRADES STYLES
   =================================== */
.subjects-section {
    margin: 20px 0;
    padding: 20px;
    border: 2px solid #e0e0e0;
    border-radius: 10px;
    background-color: #f8f9ff;
}

.subjects-container {
    width: 100%;
}

.subjects-header {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr;
    gap: 15px;
    padding: 15px;
    background-color: var(--primary-color);
    color: white;
    border-radius: 8px;
    font-weight: bold;
    margin-bottom: 10px;
}

.subject-code-header,
.grades-header,
.units-header {
    text-align: center;
}

.subjects-list {
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid #ddd;
    border-radius: 8px;
    background-color: white;
}

.subject-row {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr;
    gap: 15px;
    padding: 12px 15px;
    border-bottom: 1px solid #eee;
    align-items: center;
}

.subject-row:last-child {
    border-bottom: none;
}

.subject-row:nth-child(even) {
    background-color: #f9f9f9;
}

.subject-info {
    text-align: left;
}

.subject-code {
    font-weight: bold;
    color: var(--primary-color);
    font-size: 14px;
}

.subject-title {
    color: #666;
    font-size: 13px;
    margin-top: 2px;
}

.subject-grade {
    text-align: center;
}

.subject-grade input {
    width: 80px;
    padding: 8px;
    border: 2px solid #ddd;
    border-radius: 5px;
    text-align: center;
    font-weight: bold;
}

.subject-grade input:focus {
    border-color: var(--primary-color);
    outline: none;
}

.subject-units {
    text-align: center;
    font-weight: bold;
    color: #333;
}

/* GWA Calculation */
.gwa-calculation {
    margin-top: 20px;
    padding: 20px;
    background-color: white;
    border: 2px solid var(--primary-color);
    border-radius: 10px;
}

.gwa-row {
    display: flex;
    justify-content: space-between;
    padding: 8px 0;
    border-bottom: 1px solid #eee;
}

.gwa-row:last-child {
    border-bottom: none;
}

.gwa-final {
    background-color: var(--primary-light);
    padding: 15px;
    border-radius: 8px;
    margin-top: 10px;
    font-size: 16px;
}

.gwa-label {
    color: #333;
}

.gwa-value {
    color: var(--primary-color);
    font-weight: bold;
}

/* No Subjects Message */
.no-subjects-message {
    padding: 40px 20px;
    text-align: center;
    background-color: #f8f9fa;
    border: 2px dashed #dee2e6;
    border-radius: 10px;
    margin: 20px 0;
}

.no-subjects-content {
    max-width: 500px;
    margin: 0 auto;
}

.no-subjects-content i {
    font-size: 48px;
    color: #6c757d;
    margin-bottom: 20px;
}

.no-subjects-content h4 {
    color: #495057;
    margin-bottom: 15px;
    font-size: 24px;
}

.no-subjects-content p {
    color: #6c757d;
    margin-bottom: 15px;
    line-height: 1.5;
}

.no-subjects-content ul {
    text-align: left;
    background-color: white;
    padding: 20px;
    border-radius: 8px;
    border: 1px solid #e9ecef;
    margin: 20px 0;
    list-style: none;
}

.no-subjects-content ul li {
    padding: 8px 0;
    border-bottom: 1px solid #f1f3f4;
    color: #495057;
}

.no-subjects-content ul li:last-child {
    border-bottom: none;
}

.no-subjects-content ul li strong {
    color: var(--primary-color);
    display: inline-block;
    width: 100px;
}

.no-subjects-content .note {
    font-style: italic;
    color: #868e96;
    font-size: 14px;
    margin-top: 20px;
}


/* ===================================
   FORM VALIDATION STYLES
   =================================== */
.field-error {
    color: #dc3545;
    font-size: 0.875rem;
    margin-top: 5px;
    display: flex;
    align-items: center;
    gap: 5px;
}

.field-error i {
    font-size: 0.8rem;
}

input.error,
select.error,
textarea.error {
    border-color: #dc3545 !important;
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25) !important;
}

.duplicate-warning {
    background: linear-gradient(135deg, #fff3cd, #ffeaa7);
    border: 2px solid #dc3545;
    color: #721c24;
    padding: 15px;
    border-radius: 8px;
    margin-top: 8px;
    font-size: 0.9rem;
    box-shadow: 0 4px 12px rgba(220, 53, 69, 0.15);
    animation: slideDown 0.3s ease-out;
}

.duplicate-warning-content {
    display: flex;
    align-items: flex-start;
    gap: 12px;
}

.duplicate-warning-content i {
    color: #dc3545;
    font-size: 1.2rem;
    margin-top: 2px;
    animation: pulse 2s infinite;
}

.warning-text {
    flex: 1;
    line-height: 1.4;
}

.warning-text strong {
    color: #dc3545;
    font-weight: 600;
}

.warning-note {
    display: block;
    margin-top: 8px;
    font-size: 0.85rem;
    color: #856404;
    font-style: italic;
    background: rgba(255, 255, 255, 0.7);
    padding: 6px 10px;
    border-radius: 4px;
    border-left: 3px solid #dc3545;
}

.grantee-notice {
    display: block;
    margin: 6px 0;
    font-size: 0.9rem;
    color: #155724;
    font-weight: 600;
    background: rgba(212, 237, 218, 0.8);
    padding: 8px 12px;
    border-radius: 4px;
    border-left: 3px solid #28a745;
}

input.duplicate-error {
    border-color: #dc3545 !important;
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25) !important;
    background-color: #fff5f5 !important;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.6;
    }
}

/* Main Screen Duplicate Notification */
.main-screen-duplicate-notification {
    background: linear-gradient(135deg, #f8d7da, #f5c6cb);
    border: 2px solid #dc3545;
    border-radius: 12px;
    margin: 20px auto;
    max-width: 900px;
    box-shadow: 0 8px 25px rgba(220, 53, 69, 0.2);
    animation: slideDownBounce 0.5s ease-out;
    position: relative;
    z-index: 1000;
}

.notification-content {
    display: flex;
    align-items: flex-start;
    padding: 20px;
    gap: 15px;
}

.notification-icon {
    flex-shrink: 0;
    width: 50px;
    height: 50px;
    background: #dc3545;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    animation: pulse 2s infinite;
}

.notification-icon i {
    color: white;
    font-size: 1.5rem;
}

.notification-text {
    flex: 1;
    color: #721c24;
    font-size: 1rem;
    line-height: 1.5;
}

.notification-text strong {
    color: #dc3545;
    font-weight: 700;
    font-size: 1.1rem;
}

.status-badge {
    background: #28a745;
    color: white;
    padding: 3px 8px;
    border-radius: 12px;
    font-size: 0.85rem;
    font-weight: 600;
}

.notification-close {
    flex-shrink: 0;
    background: none;
    border: none;
    color: #dc3545;
    font-size: 1.2rem;
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    width: 35px;
    height: 35px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.notification-close:hover {
    background: rgba(220, 53, 69, 0.1);
    transform: scale(1.1);
}

@keyframes slideDownBounce {
    0% {
        opacity: 0;
        transform: translateY(-30px) scale(0.9);
    }
    60% {
        opacity: 1;
        transform: translateY(5px) scale(1.02);
    }
    100% {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* Responsive Design for Main Screen Notification */
@media (max-width: 768px) {
    .main-screen-duplicate-notification {
        margin: 15px 10px;
        max-width: calc(100% - 20px);
    }

    .notification-content {
        padding: 15px;
        gap: 10px;
    }

    .notification-icon {
        width: 40px;
        height: 40px;
    }

    .notification-icon i {
        font-size: 1.2rem;
    }

    .notification-text {
        font-size: 0.9rem;
    }

    .notification-text strong {
        font-size: 1rem;
    }
}

@media (max-width: 480px) {
    .main-screen-duplicate-notification {
        margin: 10px 5px;
    }

    .notification-content {
        padding: 12px;
        gap: 8px;
    }

    .notification-text {
        font-size: 0.85rem;
        line-height: 1.4;
    }

    .notification-close {
        width: 30px;
        height: 30px;
        font-size: 1rem;
    }
}

/* Form validation success states */
input.valid,
select.valid,
textarea.valid {
    border-color: #28a745 !important;
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25) !important;
}

.field-success {
    color: #28a745;
    font-size: 0.875rem;
    margin-top: 5px;
    display: flex;
    align-items: center;
    gap: 5px;
}

/* Loading states for validation */
.field-loading {
    position: relative;
}

.field-loading::after {
    content: '';
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    width: 16px;
    height: 16px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: translateY(-50%) rotate(0deg); }
    100% { transform: translateY(-50%) rotate(360deg); }
}

/* Enhanced alert styles */
.alert {
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 20px;
    border: 1px solid transparent;
}

.alert-danger {
    background-color: #f8d7da;
    border-color: #f5c6cb;
    color: #721c24;
}

.alert-success {
    background-color: #d4edda;
    border-color: #c3e6cb;
    color: #155724;
}

.alert ul {
    margin: 0;
    padding-left: 20px;
}

.alert li {
    margin-bottom: 5px;
}

/* ===================================
   LAYOUT IMPROVEMENTS
   =================================== */
.row {
    margin-left: -15px;
    margin-right: -15px;
}

.col-lg-8, .col-lg-4 {
    padding-left: 15px;
    padding-right: 15px;
}

/* ===================================
   ANNOUNCEMENTS STYLES
   =================================== */
.announcements-container {
    max-height: 500px;
    overflow-y: auto;
    padding-right: 5px;
}

.announcement-item {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 15px;
    border-left: 4px solid #667eea;
    transition: all 0.3s ease;
}

.announcement-item:hover {
    background: #e9ecef;
    transform: translateX(5px);
}

.announcement-item:last-child {
    margin-bottom: 0;
}

.announcement-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 10px;
    flex-wrap: wrap;
    gap: 10px;
}

.announcement-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: #2c3e50;
    margin: 0;
    line-height: 1.3;
}

.announcement-date {
    font-size: 0.85rem;
    color: #6c757d;
    display: flex;
    align-items: center;
    gap: 5px;
    white-space: nowrap;
}

.announcement-date i {
    color: #667eea;
}

.announcement-content {
    color: #495057;
    line-height: 1.5;
}

.announcement-content p {
    margin: 0 0 10px 0;
}

.announcement-content p:last-child {
    margin-bottom: 0;
}

.announcement-badge {
    display: inline-flex;
    align-items: center;
    gap: 5px;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.announcement-badge.important {
    background: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

.announcement-badge.important i {
    color: #f39c12;
}

.no-announcements {
    text-align: center;
    padding: 40px 20px;
    color: #6c757d;
}

.no-announcements-icon {
    font-size: 3rem;
    color: #dee2e6;
    margin-bottom: 15px;
}

.no-announcements-text {
    font-size: 1.1rem;
    font-weight: 500;
    margin: 0 0 5px 0;
    color: #495057;
}

.no-announcements-subtitle {
    font-size: 0.9rem;
    color: #6c757d;
}

/* ===================================
   FAQ STYLES
   =================================== */
.faq-container {
    max-height: 400px;
    overflow-y: auto;
    padding-right: 5px;
}

.faq-item {
    border-bottom: 1px solid #e9ecef;
    margin-bottom: 10px;
}

.faq-item:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.faq-question {
    display: flex;
    align-items: center;
    padding: 15px 0;
    cursor: pointer;
    font-weight: 500;
    color: var(--primary-color);
    transition: all 0.3s ease;
    user-select: none;
}

.faq-question:hover {
    color: var(--primary-dark);
}

.faq-icon {
    margin-right: 10px;
    font-size: 12px;
    transition: transform 0.3s ease;
    color: var(--primary-color);
}

.faq-question[aria-expanded="true"] .faq-icon {
    transform: rotate(90deg);
}

.faq-question span {
    flex: 1;
    font-size: 14px;
    line-height: 1.4;
}

.faq-answer {
    padding: 0 22px 15px 22px;
    color: #495057;
    font-size: 13px;
    line-height: 1.5;
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease, padding 0.3s ease;
}

.faq-answer.show {
    max-height: 200px;
    padding: 10px 22px 15px 22px;
}

.faq-answer p {
    margin: 0;
}

/* ===================================
   SETTINGS MODAL STYLES
   =================================== */
.settings-modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
}

.settings-modal-content {
    background-color: white;
    margin: 2% auto;
    border-radius: 12px;
    width: 90%;
    max-width: 800px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.settings-modal-header {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: white;
    padding: 20px 25px;
    border-radius: 12px 12px 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.settings-modal-header h2 {
    margin: 0;
    font-size: 22px;
    font-weight: 600;
}

.settings-modal-header h2 i {
    margin-right: 10px;
}

.close-settings-btn {
    background: none;
    border: none;
    color: white;
    font-size: 20px;
    cursor: pointer;
    padding: 8px;
    border-radius: 50%;
    transition: background-color 0.3s ease;
}

.close-settings-btn:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

.settings-tabs {
    display: flex;
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
}

.settings-tab {
    flex: 1;
    padding: 15px 20px;
    background: none;
    border: none;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    color: #666;
    transition: all 0.3s ease;
    border-bottom: 3px solid transparent;
}

.settings-tab:hover {
    background-color: #e9ecef;
    color: var(--primary-color);
}

.settings-tab.active {
    background-color: white;
    color: var(--primary-color);
    border-bottom-color: var(--primary-color);
}

.settings-tab i {
    margin-right: 8px;
}

.settings-modal-body {
    padding: 25px;
}

.settings-section h3 {
    color: var(--primary-color);
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid var(--primary-light);
}

/* Profile Details Styles */
.profile-details {
    display: grid;
    gap: 15px;
}

.detail-row {
    display: grid;
    grid-template-columns: 150px 1fr;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #f0f0f0;
}

.detail-row:last-child {
    border-bottom: none;
}

.detail-row label {
    font-weight: 600;
    color: #555;
    font-size: 14px;
}

.detail-row span {
    color: #333;
    font-size: 14px;
}

.student-id-badge {
    background: var(--primary-light);
    color: var(--primary-color);
    padding: 4px 12px;
    border-radius: 20px;
    font-family: 'Courier New', monospace;
    font-weight: 600;
    font-size: 13px !important;
    display: inline-block;
    width: fit-content;
}

.status-badge {
    padding: 4px 12px;
    border-radius: 20px;
    font-weight: 600;
    font-size: 12px !important;
    text-transform: uppercase;
    display: inline-block;
    width: fit-content;
}

.status-badge.active {
    background: #d4edda;
    color: #155724;
}

.status-badge.inactive {
    background: #f8d7da;
    color: #721c24;
}

.password-status {
    padding: 4px 12px;
    border-radius: 20px;
    font-weight: 600;
    font-size: 12px !important;
    display: inline-block;
    width: fit-content;
}

.password-status.changed {
    background: #d4edda;
    color: #155724;
}

.password-status.default {
    background: #fff3cd;
    color: #856404;
}

.password-warning {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-left: 4px solid #fdcb6e;
    padding: 15px;
    border-radius: 8px;
    margin-top: 20px;
    color: #856404;
}

.password-warning i {
    margin-right: 8px;
    color: #e17055;
}

/* Password Form Styles */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #555;
    font-size: 14px;
}

.password-input-container {
    position: relative;
    display: flex;
    align-items: center;
}

.password-input-container input {
    width: 100%;
    padding: 12px 45px 12px 15px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 14px;
    transition: border-color 0.3s ease;
}

.password-input-container input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(5, 47, 17, 0.1);
}

.password-input-container input.error {
    border-color: #dc3545;
    box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.1);
}

.password-input-container input.success {
    border-color: #28a745;
    box-shadow: 0 0 0 3px rgba(40, 167, 69, 0.1);
}

.password-toggle {
    position: absolute;
    right: 12px;
    background: none;
    border: none;
    color: #666;
    cursor: pointer;
    padding: 8px;
    border-radius: 4px;
    transition: color 0.3s ease;
}

.password-toggle:hover {
    color: var(--primary-color);
}

.password-hint {
    display: block;
    margin-top: 5px;
    font-size: 12px;
    color: #666;
    font-style: italic;
}

.password-error {
    background: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
    padding: 8px 12px;
    border-radius: 6px;
    margin-top: 8px;
    font-size: 13px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.password-error i {
    color: #dc3545;
}

.password-success {
    background: linear-gradient(135deg, #d4edda, #c3e6cb);
    border: 2px solid #28a745;
    border-left: 6px solid #28a745;
    color: #155724;
    padding: 18px 20px;
    border-radius: 12px;
    margin-bottom: 25px;
    font-size: 15px;
    display: flex;
    align-items: center;
    gap: 12px;
    font-weight: 600;
    box-shadow: 0 4px 12px rgba(40, 167, 69, 0.2);
    animation: successSlideIn 0.5s ease-out;
    position: relative;
    overflow: hidden;
}

.password-success::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #28a745, #20c997, #28a745);
    animation: successShimmer 2s ease-in-out infinite;
}

.password-success i {
    color: #28a745;
    font-size: 20px;
    background: white;
    padding: 8px;
    border-radius: 50%;
    box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);
}

@keyframes successSlideIn {
    from {
        opacity: 0;
        transform: translateY(-20px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

@keyframes successShimmer {
    0%, 100% {
        transform: translateX(-100%);
    }
    50% {
        transform: translateX(100%);
    }
}

@keyframes successSlideOut {
    from {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
    to {
        opacity: 0;
        transform: translateY(-20px) scale(0.95);
    }
}

.success-content {
    line-height: 1.4;
}

.success-content strong {
    font-size: 16px;
    display: block;
    margin-bottom: 4px;
}

.form-actions {
    display: flex;
    gap: 15px;
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #e9ecef;
}

.btn-primary {
    background: var(--primary-color);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.btn-primary:hover {
    background: var(--primary-dark);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(5, 47, 17, 0.3);
}

.btn-secondary {
    background: #6c757d;
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-secondary:hover {
    background: #5a6268;
    transform: translateY(-2px);
}

/* ===================================
   TOAST NOTIFICATION STYLES
   =================================== */
.toast-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 10000;
    display: flex;
    flex-direction: column;
    gap: 15px;
    max-width: 400px;
}

.toast {
    background: white;
    border-radius: 12px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    padding: 20px;
    display: flex;
    align-items: flex-start;
    gap: 15px;
    animation: toastSlideIn 0.4s ease-out;
    border-left: 5px solid;
    position: relative;
    overflow: hidden;
}

.toast-success {
    border-left-color: #28a745;
    background: linear-gradient(135deg, #ffffff, #f8fff8);
}

.toast-error {
    border-left-color: #dc3545;
    background: linear-gradient(135deg, #ffffff, #fff8f8);
}

.toast::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-color), #20c997, var(--primary-color));
    animation: toastProgress 6s linear;
}

.toast-icon {
    flex-shrink: 0;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
}

.toast-success .toast-icon {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
}

.toast-error .toast-icon {
    background: linear-gradient(135deg, #dc3545, #e74c3c);
    color: white;
}

.toast-content {
    flex: 1;
    min-width: 0;
}

.toast-title {
    font-weight: 700;
    font-size: 16px;
    color: #2c3e50;
    margin-bottom: 4px;
}

.toast-message {
    font-size: 14px;
    color: #6c757d;
    line-height: 1.4;
}

.toast-close {
    background: none;
    border: none;
    color: #adb5bd;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: all 0.2s ease;
    flex-shrink: 0;
}

.toast-close:hover {
    background: #f8f9fa;
    color: #6c757d;
}

@keyframes toastSlideIn {
    from {
        opacity: 0;
        transform: translateX(100%) scale(0.8);
    }
    to {
        opacity: 1;
        transform: translateX(0) scale(1);
    }
}

@keyframes toastSlideOut {
    from {
        opacity: 1;
        transform: translateX(0) scale(1);
    }
    to {
        opacity: 0;
        transform: translateX(100%) scale(0.8);
    }
}

@keyframes toastProgress {
    from {
        width: 100%;
    }
    to {
        width: 0%;
    }
}



/* ===================================
   RESPONSIVE DESIGN
   =================================== */
@media (max-width: 768px) {
    .container {
        padding: 0 15px;
    }

    .welcome-section {
        flex-direction: column;
        gap: 15px;
        text-align: center;
        margin: 20px 0;
        padding: 20px;
    }

    .scholarship-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .header-container {
        padding: 0 15px;
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }

    .university-logo {
        justify-content: center;
    }

    .card-body {
        padding: 15px;
    }

    .form-body {
        padding: 20px;
    }

    /* Announcements responsive */
    .announcement-header {
        flex-direction: column;
        align-items: flex-start;
    }

    .announcement-date {
        align-self: flex-end;
    }

    .announcements-container {
        max-height: 400px;
    }
}

@media (max-width: 576px) {
    .welcome-text {
        font-size: 18px;
    }

    .scholarship-card {
        padding: 20px;
    }

    .scholarship-title {
        font-size: 16px;
    }

    .apply-btn {
        width: 100%;
        justify-content: center;
    }

    .header-container {
        padding: 0 10px;
    }

    .container {
        padding: 0 10px;
    }
}
