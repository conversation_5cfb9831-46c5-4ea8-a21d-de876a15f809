@extends('layouts.student')

@section('title', 'Application Details')

@push('styles')
    <link rel="stylesheet" href="{{ asset('css/scholarship.css') }}">
@endpush

@section('content')
    <style>
        .details-container {
            text-align: center;
            padding: 3rem;
        }

        .details-icon {
            font-size: 5rem;
            color: #1b5e20;
            margin-bottom: 1.5rem;
        }

        .details-title {
            font-size: 2rem;
            margin-bottom: 1rem;
            color: #1b5e20;
        }

        .details-message {
            font-size: 1.1rem;
            margin-bottom: 2rem;
            color: #333;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
        }

        .application-details {
            background-color: #f5f5f5;
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            max-width: 500px;
            margin-left: auto;
            margin-right: auto;
            text-align: left;
        }

        .detail-row {
            display: flex;
            margin-bottom: 15px;
        }

        .detail-label {
            width: 40%;
            font-weight: 600;
            color: #555;
        }

        .detail-value {
            width: 60%;
            color: #333;
        }

        .action-buttons {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-top: 30px;
            flex-wrap: wrap;
        }

        @media (max-width: 768px) {
            .action-buttons {
                flex-direction: column;
                align-items: center;
                gap: 15px;
            }
        }

        .action-btn {
            padding: 12px 25px;
            border-radius: 5px;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 10px;
            transition: all 0.3s ease;
        }

        .back-btn {
            background-color: #f5f5f5;
            color: #333;
            border: 1px solid #ddd;
        }

        .back-btn:hover {
            background-color: #e5e5e5;
        }

        .track-btn {
            background: linear-gradient(135deg, #2e7d32, #1b5e20);
            color: white !important;
            font-size: 16px;
            font-weight: 700;
            padding: 15px 30px;
            border-radius: 8px;
            box-shadow: 0 4px 15px rgba(46, 125, 50, 0.3);
            transform: translateY(0);
            position: relative;
            overflow: hidden;
            text-decoration: none !important;
        }

        .track-btn:hover {
            background: linear-gradient(135deg, #388e3c, #2e7d32);
            color: white !important;
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(46, 125, 50, 0.4);
            text-decoration: none !important;
        }

        .status-badge {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
        }

        .status-pending {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }

        .status-approved {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status-rejected {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
    </head>

    <body>
        <!-- University Header -->
        <header class="university-header">
            <div class="header-content">
                <div class="university-logo-title">
                    <img src="{{ asset('images/5x5 ft_LOGO.png') }}" alt="St. Paul University Philippines Logo"
                        class="university-logo">
                    <div class="university-title">
                        <h1>St. Paul University Philippines</h1>
                        <h2>OFFICE OF THE REGISTRAR</h2>
                    </div>
                </div>
                <div class="user-actions">
                    <a href="{{ route('logout') }}" class="logout-btn">
                        <i class="fas fa-sign-out-alt"></i> Log Out
                    </a>
                </div>
            </div>
        </header>

        <!-- Dashboard Banner -->
        <div class="dashboard-banner">
            <div class="banner-container">
                <h2>STUDENT DASHBOARD</h2>
            </div>
        </div>

        <div class="application-container">
            <div class="details-container">
                <div class="details-icon">
                    <i class="fas fa-file-alt"></i>
                </div>
                <h1 class="details-title">Application Details</h1>
                <p class="details-message">
                    Here are the complete details of your scholarship application.
                    You can track the status and view all submitted information.
                </p>

                <div class="application-details">
                    <div class="detail-row">
                        <div class="detail-label">Application ID:</div>
                        <div class="detail-value">{{ $application->application_id }}</div>
                    </div>
                    <div class="detail-row">
                        <div class="detail-label">Student ID:</div>
                        <div class="detail-value">{{ $application->student_id }}</div>
                    </div>
                    <div class="detail-row">
                        <div class="detail-label">Full Name:</div>
                        <div class="detail-value">
                            {{ $application->first_name }}
                            @if ($application->middle_name)
                                {{ $application->middle_name }}
                            @endif
                            {{ $application->last_name }}
                        </div>
                    </div>
                    <div class="detail-row">
                        <div class="detail-label">Scholarship Type:</div>
                        <div class="detail-value">
                            @if ($application->scholarship_type == 'government')
                                Government Scholarship
                            @elseif($application->scholarship_type == 'academic')
                                Academic Scholarship
                            @elseif($application->scholarship_type == 'employees')
                                Employees Scholar
                            @elseif($application->scholarship_type == 'private')
                                Private Scholarship
                            @else
                                {{ ucfirst($application->scholarship_type) }} Scholarship
                            @endif
                        </div>
                    </div>
                    <div class="detail-row">
                        <div class="detail-label">Submission Date:</div>
                        <div class="detail-value">{{ $application->created_at->format('F d, Y') }}</div>
                    </div>
                    <div class="detail-row">
                        <div class="detail-label">Current Status:</div>
                        <div class="detail-value">
                            <span class="status-badge status-{{ strtolower($application->status) }}">
                                {{ $application->status }}
                            </span>
                        </div>
                    </div>
                    @if ($application->email)
                        <div class="detail-row">
                            <div class="detail-label">Email:</div>
                            <div class="detail-value">{{ $application->email }}</div>
                        </div>
                    @endif
                    @if ($application->contact_number)
                        <div class="detail-row">
                            <div class="detail-label">Contact Number:</div>
                            <div class="detail-value">{{ $application->contact_number }}</div>
                        </div>
                    @endif
                    @if ($application->education_stage)
                        <div class="detail-row">
                            <div class="detail-label">Education Level:</div>
                            <div class="detail-value">{{ $application->education_stage }}</div>
                        </div>
                    @endif
                    @if ($application->course)
                        <div class="detail-row">
                            <div class="detail-label">Course:</div>
                            <div class="detail-value">{{ $application->course }}</div>
                        </div>
                    @endif
                    @if ($application->strand)
                        <div class="detail-row">
                            <div class="detail-label">Strand:</div>
                            <div class="detail-value">{{ $application->strand }}</div>
                        </div>
                    @endif
                    @if ($application->year_level)
                        <div class="detail-row">
                            <div class="detail-label">Year Level:</div>
                            <div class="detail-value">{{ $application->year_level }}</div>
                        </div>
                    @endif
                    @if ($application->grade_level)
                        <div class="detail-row">
                            <div class="detail-label">Grade Level:</div>
                            <div class="detail-value">{{ $application->grade_level }}</div>
                        </div>
                    @endif
                    @if ($application->gwa)
                        <div class="detail-row">
                            <div class="detail-label">GWA:</div>
                            <div class="detail-value">{{ $application->gwa }}</div>
                        </div>
                    @endif
                    @if ($application->government_benefactor_type)
                        <div class="detail-row">
                            <div class="detail-label">Benefactor Type:</div>
                            <div class="detail-value">{{ $application->government_benefactor_type }}</div>
                        </div>
                    @endif
                </div>

                <!-- Additional Information Section -->
                @if ($application->father_first_name || $application->mother_first_name || $application->street)
                    <div class="application-details" style="margin-top: 20px;">
                        <h3 style="text-align: center; margin-bottom: 15px; color: #1b5e20;">Additional Information</h3>

                        @if ($application->father_first_name)
                            <div class="detail-row">
                                <div class="detail-label">Father's Name:</div>
                                <div class="detail-value">{{ $application->father_first_name }}
                                    {{ $application->father_last_name }}</div>
                            </div>
                        @endif

                        @if ($application->mother_first_name)
                            <div class="detail-row">
                                <div class="detail-label">Mother's Name:</div>
                                <div class="detail-value">{{ $application->mother_first_name }}
                                    {{ $application->mother_last_name }}</div>
                            </div>
                        @endif

                        @if ($application->street)
                            <div class="detail-row">
                                <div class="detail-label">Address:</div>
                                <div class="detail-value">
                                    {{ $application->street }}, {{ $application->barangay }},
                                    {{ $application->city }}, {{ $application->province }} {{ $application->zipcode }}
                                </div>
                            </div>
                        @endif

                        @if ($application->birthdate)
                            <div class="detail-row">
                                <div class="detail-label">Birthdate:</div>
                                <div class="detail-value">
                                    {{ \Carbon\Carbon::parse($application->birthdate)->format('F d, Y') }}</div>
                            </div>
                        @endif

                        @if ($application->sex)
                            <div class="detail-row">
                                <div class="detail-label">Sex:</div>
                                <div class="detail-value">{{ $application->sex }}</div>
                            </div>
                        @endif
                    </div>
                @endif

                <div class="action-buttons">
                    <a href="{{ route('student.dashboard') }}" class="action-btn back-btn">
                        <i class="fas fa-arrow-left"></i> Back to Dashboard
                    </a>
                    <a href="{{ route('scholarship.tracker', ['id' => $application->application_id]) }}"
                        class="action-btn track-btn">
                        <i class="fas fa-search"></i> Track Application Status
                    </a>
                </div>
            </div>
        </div>

        <script>
            document.addEventListener('DOMContentLoaded', function() {
                console.log('Application details page loaded');
            });
        </script>
    </body>

    </html>
